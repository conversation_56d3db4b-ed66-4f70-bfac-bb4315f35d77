#!/usr/bin/env python3
"""
B端智能助手-APA 图标生成器
生成Chrome扩展所需的图标文件
"""

import os
from PIL import Image, ImageDraw, ImageFont
import sys

def create_icon(size):
    """创建指定尺寸的图标"""
    # 创建画布
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)

    # 根据尺寸调整边距
    margin = max(1, size // 16)

    # 背景圆形
    draw.ellipse([margin, margin, size - margin, size - margin],
                fill=(102, 126, 234, 255))  # #667eea

    # 添加机器人图标（根据尺寸调整比例）
    center = size // 2

    # 机器人头部 - 调整为更大的比例
    if size <= 16:
        head_size = size - 6
    elif size <= 32:
        head_size = size - 8
    elif size <= 48:
        head_size = size - 12
    else:  # 128
        head_size = size - 32

    head_x = center - head_size // 2
    head_y = center - head_size // 2

    # 绘制机器人头部（白色圆角矩形）
    corner_radius = max(2, head_size // 8)

    # 简化版圆角矩形
    draw.rectangle([head_x + corner_radius, head_y,
                   head_x + head_size - corner_radius, head_y + head_size],
                  fill=(255, 255, 255, 255))
    draw.rectangle([head_x, head_y + corner_radius,
                   head_x + head_size, head_y + head_size - corner_radius],
                  fill=(255, 255, 255, 255))

    # 四个圆角
    draw.ellipse([head_x, head_y, head_x + corner_radius*2, head_y + corner_radius*2],
                fill=(255, 255, 255, 255))
    draw.ellipse([head_x + head_size - corner_radius*2, head_y,
                 head_x + head_size, head_y + corner_radius*2],
                fill=(255, 255, 255, 255))
    draw.ellipse([head_x, head_y + head_size - corner_radius*2,
                 head_x + corner_radius*2, head_y + head_size],
                fill=(255, 255, 255, 255))
    draw.ellipse([head_x + head_size - corner_radius*2, head_y + head_size - corner_radius*2,
                 head_x + head_size, head_y + head_size],
                fill=(255, 255, 255, 255))

    # 绘制眼睛 - 根据尺寸调整
    if size <= 16:
        eye_size = 2
    elif size <= 32:
        eye_size = 3
    elif size <= 48:
        eye_size = 4
    else:
        eye_size = 8

    eye_y = head_y + head_size // 3
    eye_spacing = head_size // 4
    left_eye_x = center - eye_spacing
    right_eye_x = center + eye_spacing

    draw.ellipse([left_eye_x - eye_size//2, eye_y - eye_size//2,
                 left_eye_x + eye_size//2, eye_y + eye_size//2],
                fill=(102, 126, 234, 255))
    draw.ellipse([right_eye_x - eye_size//2, eye_y - eye_size//2,
                 right_eye_x + eye_size//2, eye_y + eye_size//2],
                fill=(102, 126, 234, 255))

    # 绘制嘴巴 - 根据尺寸调整
    mouth_y = head_y + 2 * head_size // 3

    if size <= 16:
        mouth_width = 4
        mouth_height = 1
    elif size <= 32:
        mouth_width = 8
        mouth_height = 2
    elif size <= 48:
        mouth_width = 12
        mouth_height = 2
    else:
        mouth_width = 24
        mouth_height = 4

    mouth_x = center - mouth_width // 2

    draw.rectangle([mouth_x, mouth_y, mouth_x + mouth_width, mouth_y + mouth_height],
                  fill=(102, 126, 234, 255))

    return img

def generate_all_icons():
    """生成所有尺寸的图标"""
    sizes = [16, 32, 48, 128]

    # 确保目录存在
    icons_dir = 'assets/icons'
    os.makedirs(icons_dir, exist_ok=True)

    for size in sizes:
        print(f"生成 {size}x{size} 图标...")
        icon = create_icon(size)
        filename = f"{icons_dir}/icon{size}.png"
        icon.save(filename, 'PNG')
        print(f"已保存: {filename}")

    print("\n✅ 所有图标生成完成！")
    print("现在可以重新加载Chrome扩展了。")

if __name__ == "__main__":
    try:
        generate_all_icons()
    except ImportError:
        print("❌ 错误: 需要安装 Pillow 库")
        print("请运行: pip install Pillow")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 生成图标时出错: {e}")
        sys.exit(1)
